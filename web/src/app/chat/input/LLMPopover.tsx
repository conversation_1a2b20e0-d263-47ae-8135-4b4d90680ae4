import React, {
  useState,
  useEffect,
  useCallback,
  useLayoutEffect,
} from "react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ChatInputOption } from "./ChatInputOption";
import { getDisplayNameForModel } from "@/lib/hooks";
import {
  checkLLMSupportsImageInput,
  destructureValue,
  structureValue,
} from "@/lib/llm/utils";
import {
  getProviderIcon,
  LLMProviderDescriptor,
} from "@/app/admin/configuration/llm/interfaces";
import { Persona } from "@/app/admin/assistants/interfaces";
import { LlmManager } from "@/lib/hooks";

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { FiAlertTriangle } from "react-icons/fi";

import { Slider } from "@/components/ui/slider";
import { useUser } from "@/components/user/UserProvider";
import { TruncatedText } from "@/components/ui/truncatedText";

interface LLMPopoverProps {
  llmProviders: LLMProviderDescriptor[];
  llmManager: LlmManager;
  requiresImageGeneration?: boolean;
  currentAssistant?: Persona;
}

export default function LLMPopover({
  llmProviders,
  llmManager,
  requiresImageGeneration,
  currentAssistant,
}: LLMPopoverProps) {
  // All hooks must be called at the top level, before any early returns
  const [isOpen, setIsOpen] = useState(false);
  const { user } = useUser();
  const [localTemperature, setLocalTemperature] = useState(
    llmManager.temperature ?? 0.5
  );

  useEffect(() => {
    setLocalTemperature(llmManager.temperature ?? 0.5);
  }, [llmManager.temperature]);

  // Ensure selected model is always valid for the current assistant
  useEffect(() => {
    if (!currentAssistant) return;
    // If assistant restricts available models
    if (currentAssistant.available_llm_models && currentAssistant.available_llm_models.length > 0) {
      const currentStructured = structureValue(
        llmManager.currentLlm.name,
        llmManager.currentLlm.provider,
        llmManager.currentLlm.modelName
      );
      // If current model is not in allowed list, reset
      if (!currentAssistant.available_llm_models.includes(currentStructured)) {
        // Prefer assistant's default override if present and valid
        let newModelValue: string | null = null;
        if (
          currentAssistant.llm_model_version_override &&
          currentAssistant.llm_model_provider_override
        ) {
          // Compose the structured value for the override
          const providerObj = llmProviders.find(
            (p) => p.name === currentAssistant.llm_model_provider_override
          );
          if (providerObj) {
            const overrideStructured = structureValue(
              providerObj.name,
              providerObj.provider,
              currentAssistant.llm_model_version_override
            );
            if (currentAssistant.available_llm_models.includes(overrideStructured)) {
              newModelValue = overrideStructured;
            }
          }
        }
        // Otherwise, just use the first available model
        if (!newModelValue && currentAssistant.available_llm_models.length > 0) {
          newModelValue = currentAssistant.available_llm_models[0];
        }
        if (newModelValue) {
          llmManager.updateCurrentLlm(destructureValue(newModelValue));
        }
      }
    }
  }, [currentAssistant, llmProviders]);

  const llmOptionsByProvider: {
    [provider: string]: {
      name: string;
      displayName: string;
      value: string;
      icon: React.FC<{ size?: number; className?: string }>;
    }[];
  } = {};

  llmProviders.forEach((llmProvider) => {
    if (!llmOptionsByProvider[llmProvider.provider]) {
      llmOptionsByProvider[llmProvider.provider] = [];
    }

    (llmProvider.display_model_names || llmProvider.model_names).forEach(
      (modelName) => {
        llmOptionsByProvider[llmProvider.provider].push({
          name: modelName,
          displayName: llmProvider.name,
          value: structureValue(
            llmProvider.name,
            llmProvider.provider,
            modelName
          ),
          icon: getProviderIcon(llmProvider.provider, modelName),
        });
      }
    );
  });

  let llmOptions = Object.entries(llmOptionsByProvider).flatMap(
    ([provider, options]) => [...options]
  );


  // Since is_default_provider was removed in favor of team-specific defaults,
  // use the first available provider as fallback
  const defaultProvider = llmProviders.length > 0 ? llmProviders[0] : null;

  const defaultModelName = defaultProvider?.default_model_name;
  const defaultModelDisplayName = defaultModelName
    ? getDisplayNameForModel(defaultModelName)
    : null;

  const handleTemperatureChange = (value: number[]) => {
    setLocalTemperature(value[0]);
  };

  const handleTemperatureChangeComplete = (value: number[]) => {
    llmManager.updateTemperature(value[0]);
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <button
          className="dark:text-[#fff] text-[#000] focus:outline-none"
          data-testid="llm-popover-trigger"
        >
          <ChatInputOption
            minimize
            toggle
            flexPriority="stiff"
            name={getDisplayNameForModel(
                llmManager?.currentLlm.modelName || 
                  defaultProvider?.name || 
                  "Models")}
            Icon={getProviderIcon(
              llmManager?.currentLlm.provider ||
                defaultProvider?.provider ||
                "anthropic",
              llmManager?.currentLlm.modelName ||
                defaultProvider?.default_model_name ||
                "claude-3-5-sonnet-20240620"
            )}
            tooltipContent="Switch models"
          />
        </button>
      </PopoverTrigger>
      <PopoverContent
        align="start"
        className="w-64 p-1 bg-background border border-background-200 rounded-md shadow-lg flex flex-col"
      >
        <div className="flex-grow max-h-[300px] default-scrollbar overflow-y-auto">
          {llmOptions.map(({ name, displayName, icon, value }) => {
            if (!requiresImageGeneration || checkLLMSupportsImageInput(name)) {
              return (
                <button
                  key={value}
                  className={`w-full flex items-center gap-x-2 px-3 py-2 text-sm text-left hover:bg-background-100 dark:hover:bg-neutral-800 transition-colors duration-150 ${
                    structureValue(
                      llmManager.currentLlm.name,
                      llmManager.currentLlm.provider,
                      llmManager.currentLlm.modelName
                    ) === value
                      ? "bg-background-100 dark:bg-neutral-900 text-text"
                      : "text-text-darker"
                  }`}
                  onClick={() => {
                    llmManager.updateCurrentLlm(destructureValue(value));
                    setIsOpen(false);
                  }}
                >
                  {icon({
                    size: 16,
                    className: "flex-none my-auto text-black",
                  })}
                  <TruncatedText text={getDisplayNameForModel(name)} />
                  {/* Show model name as secondary text */}
                  <span className="text-xs text-text-500 ml-1">
                    ({displayName})
                  </span>
                  {(() => {
                    // Check if this is the assistant's configured model
                    if (currentAssistant?.llm_model_version_override === name &&
                        currentAssistant?.llm_model_provider_override === displayName) {
                      return (
                        <span className="flex-none ml-auto text-xs">
                          (assistant)
                        </span>
                      );
                    }
                  })()}
                  {llmManager.imageFilesPresent &&
                    !checkLLMSupportsImageInput(name) && (
                      <TooltipProvider>
                        <Tooltip delayDuration={0}>
                          <TooltipTrigger className="my-auto flex items-center ml-auto">
                            <FiAlertTriangle className="text-alert" size={16} />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="text-xs">
                              This LLM is not vision-capable and cannot process
                              image files present in your chat session.
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                </button>
              );
            }
            return null;
          })}
        </div>
        {user?.preferences?.temperature_override_enabled && (
          <div className="mt-2 pt-2 border-t border-background-200">
            <div className="w-full px-3 py-2">
              <Slider
                value={[localTemperature]}
                max={llmManager.maxTemperature}
                min={0}
                step={0.01}
                onValueChange={handleTemperatureChange}
                onValueCommit={handleTemperatureChangeComplete}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-text-500 mt-2">
                <span>Temperature (creativity)</span>
                <span>{localTemperature.toFixed(1)}</span>
              </div>
            </div>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
}
