import { useState } from "react";
import { PopupSpec } from "@/components/admin/connectors/Popup";
import {
  Table,
  TableHead,
  TableRow,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import CenteredPageSelector from "./CenteredPageSelector";
import { ThreeDotsLoader } from "@/components/Loading";
import { InvitedUserSnapshot, USER_ROLE_LABELS, USER_STATUS_LABELS, UserRole } from "@/lib/types";
import { TableHeader } from "@/components/ui/table";
import { InviteUserButton } from "./buttons/InviteUserButton";
import { ErrorCallout } from "@/components/ErrorCallout";
import { FetchError } from "@/lib/fetcher";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useUser } from "@/components/user/UserProvider";

const USERS_PER_PAGE = 10;

interface Props {
  users: InvitedUserSnapshot[];
  setPopup: (spec: PopupSpec) => void;
  mutate: () => void;
  error: FetchError | null;
  isLoading: boolean;
  q: string;
}



const InvitedUserTable = ({
  users,
  setPopup,
  mutate,
  error,
  isLoading,
  q,
}: Props) => {
  const [currentPageNum, setCurrentPageNum] = useState<number>(1);
  const [selectedRoles, setSelectedRoles] = useState<UserRole[]>([]);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const { user: currentUser } = useUser();
  if (!users.length)
    return <p>Users that have been invited will show up here</p>;

  const totalPages = Math.ceil(users.length / USERS_PER_PAGE);

  // Filter users based on the search query, role, and status
  let filteredUsers = users;
  if (q) {
    filteredUsers = filteredUsers.filter((user) => user.email.includes(q));
  }
  if (selectedRoles.length > 0) {
    filteredUsers = filteredUsers.filter(
      (user) => user.role && selectedRoles.includes(user.role)
    );
  }
  if (statusFilter !== "all") {
    filteredUsers = filteredUsers.filter(
      (user) => user.status === statusFilter
    );
  }

  // Get the current page of users
  const currentPageOfUsers = filteredUsers.slice(
    (currentPageNum - 1) * USERS_PER_PAGE,
    currentPageNum * USERS_PER_PAGE
  );

  if (isLoading) {
    return <ThreeDotsLoader />;
  }

  if (error) {
    return (
      <ErrorCallout
        errorTitle="Error loading users"
        errorMsg={error?.info?.detail}
      />
    );
  }

  // Filter UI
  const renderFilters = () => (
    <div className="flex items-center gap-4 py-4">
      {/* Status Filter */}
      <Select
        value={statusFilter}
        onValueChange={setStatusFilter}
      >
        <SelectTrigger className="w-[220px] h-[34px] bg-neutral">
          <SelectValue>
            {statusFilter === "all"
              ? "All Statuses"
              : USER_STATUS_LABELS[statusFilter] || statusFilter}
          </SelectValue>
        </SelectTrigger>
        <SelectContent className="bg-background-50">
          <SelectItem value="all">All Statuses</SelectItem>
          <SelectItem value="pending_assignment">Pending Assignment</SelectItem>
          <SelectItem value="ready_to_signup">Ready to Signup</SelectItem>
        </SelectContent>
      </Select>
      {/* Role Filter */}
      <Select value="roles">
        <SelectTrigger className="w-[260px] h-[34px] bg-neutral">
          <SelectValue>
            {selectedRoles.length
              ? `${selectedRoles.length} role(s) selected`
              : "All Roles"}
          </SelectValue>
        </SelectTrigger>
        <SelectContent className="bg-background-50">
          {Object.entries(USER_ROLE_LABELS)
            .filter(([role]) => {
                if (currentUser?.role === UserRole.ADMIN) {
                  return role === UserRole.ADMIN || role === UserRole.TEAM_ADMIN || role === UserRole.BASIC;
                }
                else {
                  return role === UserRole.TEAM_ADMIN || role === UserRole.BASIC;
                }
              })
            .map(([role, label]) => (
              <div
                key={role}
                className="flex items-center space-x-2 px-2 py-1.5 cursor-pointer hover:bg-background-200"
                onClick={() => {
                  setSelectedRoles((prev) =>
                    prev.includes(role as UserRole)
                      ? prev.filter((r) => r !== role)
                      : [...prev, role as UserRole]
                  );
                }}
              >
                <input
                  type="checkbox"
                  checked={selectedRoles.includes(role as UserRole)}
                  onChange={(e) => e.stopPropagation()}
                />
                <label className="text-sm font-normal">{label}</label>
              </div>
            ))}
        </SelectContent>
      </Select>
      {/* Selected Roles Chips */}
      <div className="flex gap-2 py-1">
        {selectedRoles.map((role) => (
          <button
            key={role}
            className="border border-background-300 bg-neutral p-1 rounded text-sm hover:bg-background-200"
            onClick={() =>
              setSelectedRoles((prev) => prev.filter((r) => r !== role))
            }
            style={{ padding: "2px 8px" }}
          >
            <span>{USER_ROLE_LABELS[role]}</span>
            <span className="ml-3">&times;</span>
          </button>
        ))}
      </div>
    </div>
  );

  return (
    <>
      {renderFilters()}
      <Table className="overflow-visible">
        <TableHeader>
          <TableRow>
            <TableHead>Email</TableHead>
            <TableHead>Role</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>
              <div className="flex justify-end">Actions</div>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {currentPageOfUsers.length ? (
            currentPageOfUsers.map((user) => (
              <TableRow key={user.email}>
                <TableCell>{user.email}</TableCell>
                <TableCell>
                  {user.role
                    ? USER_ROLE_LABELS[user.role]
                    : USER_ROLE_LABELS[UserRole.BASIC]}
                </TableCell>
                <TableCell>
                  {USER_STATUS_LABELS[user.status || ""] || user.status || "-"}
                </TableCell>
                <TableCell>
                  <div className="flex justify-end">
                    <InviteUserButton
                      user={user}
                      invited={true}
                      setPopup={setPopup}
                      mutate={mutate}
                    />
                  </div>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={3} className="h-24 text-center">
                {`No users found matching "${q}"`}
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      {totalPages > 1 ? (
        <CenteredPageSelector
          currentPage={currentPageNum}
          totalPages={totalPages}
          onPageChange={setCurrentPageNum}
        />
      ) : null}
    </>
  );
};

export default InvitedUserTable;
