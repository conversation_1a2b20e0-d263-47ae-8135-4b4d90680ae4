import React from "react";
import { Persona } from "@/app/admin/assistants/interfaces";
import { LlmDescriptor } from "@/lib/hooks";
import { LLMProviderDescriptor } from "@/app/admin/configuration/llm/interfaces";

interface LLMDebugInfoProps {
  liveAssistant: Persona | null;
  llmManager: {
    currentLlm: LlmDescriptor;
  };
  llmProviders: LLMProviderDescriptor[];
}

export const LLMDebugInfo: React.FC<LLMDebugInfoProps> = ({
  liveAssistant,
  llmManager,
  llmProviders,
}) => {
  if (!liveAssistant) return null;

  const resolvedProvider = liveAssistant.llm_model_provider_override && 
    liveAssistant.llm_model_provider_override !== "USER_DEFAULT" &&
    liveAssistant.llm_model_provider_override.trim() !== ""
    ? liveAssistant.llm_model_provider_override 
    : llmManager.currentLlm.name;

  const resolvedModel = liveAssistant.llm_model_version_override && 
    liveAssistant.llm_model_version_override !== "USER_DEFAULT" &&
    liveAssistant.llm_model_version_override.trim() !== ""
    ? liveAssistant.llm_model_version_override 
    : llmManager.currentLlm.modelName;

  return (
    <div className="fixed top-4 right-4 bg-white border border-gray-300 p-4 rounded shadow-lg z-50 max-w-md">
      <h3 className="font-bold text-sm mb-2">LLM Debug Info</h3>
      
      <div className="text-xs space-y-1">
        <div>
          <strong>Assistant:</strong> {liveAssistant.name}
        </div>
        
        <div className="border-t pt-2">
          <strong>Persona Overrides:</strong>
          <div className="ml-2">
            <div>Provider: {liveAssistant.llm_model_provider_override || "null"}</div>
            <div>Model: {liveAssistant.llm_model_version_override || "null"}</div>
          </div>
        </div>
        
        <div className="border-t pt-2">
          <strong>LLM Manager:</strong>
          <div className="ml-2">
            <div>Name: {llmManager.currentLlm.name || "null"}</div>
            <div>Provider: {llmManager.currentLlm.provider || "null"}</div>
            <div>Model: {llmManager.currentLlm.modelName || "null"}</div>
          </div>
        </div>
        
        <div className="border-t pt-2">
          <strong>Final Values (for chat):</strong>
          <div className="ml-2">
            <div>Provider: <span className="font-mono bg-gray-100 px-1">{resolvedProvider || "undefined"}</span></div>
            <div>Model: <span className="font-mono bg-gray-100 px-1">{resolvedModel || "undefined"}</span></div>
          </div>
        </div>
        
        <div className="border-t pt-2">
          <strong>Available Providers:</strong>
          <div className="ml-2 max-h-20 overflow-y-auto">
            {llmProviders.map(p => (
              <div key={p.name} className="text-xs">
                {p.name} ({p.provider})
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
