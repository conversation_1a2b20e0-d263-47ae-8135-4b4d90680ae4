# LLM Override Fix Test

## Problem Description
When creating an assistant with "User Default" model selection, the chat was failing with "llm_override parameter is missing" error because the frontend was sending display names instead of actual provider names.

## Root Cause
1. Assistant created with "User Default" stores "USER_DEFAULT" markers in the database
2. Backend resolves these markers to actual provider names when persona is fetched
3. Frontend was using `llmManager.currentLlm` which might contain display names
4. Backend expects actual provider names in `llm_override.model_provider`

## Fix Applied
Modified `web/src/app/chat/ChatPage.tsx` to prioritize resolved persona LLM information:

### Before (Problematic):
```typescript
modelProvider: modelOverride?.name || llmManager.currentLlm.name || undefined,
modelVersion: modelOverride?.modelName || llmManager.currentLlm.modelName || undefined,
```

### After (Fixed):
```typescript
modelProvider:
  modelOverride?.name || 
  (() => {
    // If the assistant has LLM overrides (resolved from USER_DEFAULT), use those
    if (liveAssistant?.llm_model_provider_override && liveAssistant.llm_model_provider_override !== "USER_DEFAULT") {
      // Use the resolved provider name directly from the persona
      return liveAssistant.llm_model_provider_override;
    }
    return llmManager.currentLlm.name;
  })() || 
  undefined,
modelVersion:
  modelOverride?.modelName ||
  (() => {
    // If the assistant has LLM overrides (resolved from USER_DEFAULT), use those
    if (liveAssistant?.llm_model_version_override && liveAssistant.llm_model_version_override !== "USER_DEFAULT") {
      // Use the resolved model name directly from the persona
      return liveAssistant.llm_model_version_override;
    }
    return llmManager.currentLlm.modelName;
  })() ||
  searchParams.get(SEARCH_PARAM_NAMES.MODEL_VERSION) ||
  undefined,
```

## How It Works
1. **Check for resolved persona overrides**: First checks if the persona has resolved LLM overrides (not "USER_DEFAULT")
2. **Use resolved values**: If found, uses the actual provider name and model name from the persona
3. **Fallback to LLM manager**: Only falls back to `llmManager.currentLlm` if no persona overrides exist
4. **Consistent behavior**: Applied the same logic to ShareChatSessionModal for consistency

## Expected Result
- Assistants created with "User Default" should now send chat messages successfully
- The correct provider name (not display name) is sent in the `llm_override`
- No more "llm_override parameter is missing" or "Invalid model or provider given!" errors
- Manual model selection continues to work as before

## Testing Steps
1. Create an assistant with "User Default" model selection
2. Start a chat with that assistant
3. Send a message
4. Verify message sends successfully without errors
5. Check that the correct LLM provider is used for the response

## Files Modified
- `web/src/app/chat/ChatPage.tsx`: Main fix for chat message sending
- Applied to both regular chat and ShareChatSessionModal
